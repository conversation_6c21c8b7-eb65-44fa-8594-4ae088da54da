.btn {
  background-color: blueviolet;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
}
body {
  background-color: #f2f2f2;
}
table {
  border-collapse: collapse;
  width: 100%;
}
th,
td {
  padding: 10px;
  text-align: left;
  border: 1px solid #938e8e;
}
th {
  background-color: #4caf50;
  color: white;
}
.home {
  text-align: center;
  background-color: #4caf50;
  color: white;
}
header {
  background-color: rgb(29, 29, 29);
  padding: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.logo {
  color: white;
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
}
nav ul {
  display: flex;
  justify-content: center;
  gap: 30px;
  list-style: none;
  margin: 0;
}
nav li a {
  color: white;
  text-decoration: none;
}
